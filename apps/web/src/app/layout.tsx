import { ComparisonProvider } from '@/contexts/ComparisonContext';
import { AuthProvider } from '@/lib/auth';
import { QueryProvider } from '@/lib/query-client';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Revalu - Property Intelligence Platform',
  description: 'Advanced property valuation and market intelligence platform',
  keywords: ['property', 'real estate', 'valuation', 'market analysis', 'Australia'],
  authors: [{ name: 'Revalu Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Revalu - Property Intelligence Platform',
    description: 'Advanced property valuation and market intelligence platform',
    type: 'website',
    locale: 'en_AU',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Import components dynamically to avoid auto-formatter issues
  const { ThemeProvider } = require('@/contexts/ThemeContext');
  const { LayoutWrapper } = require('@/components/layout/LayoutWrapper');

  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gradient-hero`}>
        <QueryProvider>
          <AuthProvider>
            <ThemeProvider>
              <ComparisonProvider>
                <LayoutWrapper>
                  {children}
                </LayoutWrapper>
              </ComparisonProvider>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  className: 'dark:!bg-gray-800 dark:!text-gray-100 dark:!border-gray-700',
                  style: {
                    background: 'var(--gradient-card)',
                    color: 'rgb(var(--color-surface-foreground))',
                    borderRadius: 'var(--radius-card)',
                    border: '1px solid rgb(226 232 240)',
                    boxShadow: 'var(--shadow-card)',
                  },
                  success: {
                    duration: 3000,
                    className: 'dark:!bg-green-900/20 dark:!text-green-400 dark:!border-green-800',
                    iconTheme: {
                      primary: '#10b981',
                      secondary: '#fff',
                    },
                    style: {
                      background: 'linear-gradient(135deg, rgb(240 253 244) 0%, rgb(220 252 231) 100%)',
                      color: 'rgb(21 128 61)',
                      border: '1px solid rgb(187 247 208)',
                    },
                  },
                  error: {
                    duration: 5000,
                    className: 'dark:!bg-red-900/20 dark:!text-red-400 dark:!border-red-800',
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#fff',
                    },
                    style: {
                      background: 'linear-gradient(135deg, rgb(254 242 242) 0%, rgb(254 226 226) 100%)',
                      color: 'rgb(185 28 28)',
                      border: '1px solid rgb(252 165 165)',
                    },
                  },
                }}
              />
            </ThemeProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
