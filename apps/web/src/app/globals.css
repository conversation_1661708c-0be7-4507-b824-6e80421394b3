@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Enhanced Design System CSS Variables */
:root {
  /* Enhanced Color System */
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 100 116 139;
  --color-secondary-foreground: 15 23 42;
  --color-surface: 255 255 255;
  --color-surface-foreground: 15 23 42;
  --color-sidebar: 15 23 42;
  --color-sidebar-foreground: 248 250 252;

  /* Enhanced Gradient System */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  --gradient-secondary: linear-gradient(135deg, #64748b 0%, #475569 50%, #334155 100%);
  --gradient-surface: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  --gradient-sidebar: linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  --gradient-card: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
  --gradient-hero: linear-gradient(135deg, #eff6ff 0%, #dbeafe 25%, #bfdbfe 50%, #93c5fd 75%, #60a5fa 100%);
  --gradient-hero-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);

  /* Enhanced Shadow System */
  --shadow-card: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-card-hover: 0 25px 50px -12px rgb(0 0 0 / 0.25), 0 8px 16px -4px rgb(0 0 0 / 0.1);
  --shadow-sidebar: 4px 0 24px 0 rgb(0 0 0 / 0.15);
  --shadow-dramatic: 0 32px 64px -12px rgb(0 0 0 / 0.25);
  --shadow-glow: 0 0 20px rgb(59 130 246 / 0.3);

  /* Enhanced Border Radius System */
  --radius-card: 1rem;
  --radius-button: 0.75rem;
  --radius-input: 0.75rem;
  --radius-modal: 1.5rem;
  --radius-large: 2rem;

  /* Enhanced Spacing System */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 0.75rem;    /* 12px */
  --spacing-lg: 1rem;       /* 16px */
  --spacing-xl: 1.5rem;     /* 24px */
  --spacing-2xl: 2rem;      /* 32px */
  --spacing-3xl: 2.5rem;    /* 40px */
  --spacing-4xl: 3rem;      /* 48px */
  --spacing-5xl: 4rem;      /* 64px */

  /* Component-specific spacing */
  --spacing-card-compact: 1.5rem;    /* 24px */
  --spacing-card-default: 2rem;      /* 32px */
  --spacing-card-large: 2.5rem;      /* 40px */
  --spacing-section: 3rem;           /* 48px */
  --spacing-page: 2.5rem;            /* 40px */
}

/* Enhanced Dark Mode Variables */
.dark {
  --color-surface: 30 41 59;
  --color-surface-foreground: 248 250 252;
  --color-secondary: 148 163 184;
  --color-secondary-foreground: 248 250 252;

  /* Dark mode gradients */
  --gradient-card: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  --gradient-surface: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  --gradient-hero: var(--gradient-hero-dark);

  /* Enhanced dark mode shadows */
  --shadow-card: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-card-hover: 0 25px 50px -12px rgb(0 0 0 / 0.4), 0 8px 16px -4px rgb(0 0 0 / 0.3);
  --shadow-sidebar: 4px 0 24px 0 rgb(0 0 0 / 0.3);
  --shadow-dramatic: 0 32px 64px -12px rgb(0 0 0 / 0.4);
  --shadow-glow: 0 0 20px rgb(59 130 246 / 0.4);
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
    height: 100%;
  }

  body {
    @apply text-gray-900 antialiased min-h-screen;
    @apply dark:text-gray-100;
    background: var(--gradient-hero);
    transition: all 0.3s ease;
    height: 100%;
    min-height: 100vh;
  }

  /* Enhanced transitions for theme switching */
  * {
    transition-property: background-color, border-color, color, fill, stroke, box-shadow, transform;
    transition-duration: 0.2s;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer components {
  /* Dramatically Enhanced Card System */
  .card {
    @apply rounded-card border border-gray-200/30 dark:border-gray-700/30;
    @apply backdrop-blur-sm transition-all duration-300;
    background: var(--gradient-card);
    box-shadow: var(--shadow-card);
  }

  .card:hover {
    box-shadow: var(--shadow-card-hover);
    transform: translateY(-2px);
  }

  .card-compact {
    @apply card;
    padding: var(--spacing-card-compact);
    border-radius: var(--radius-card);
  }

  .card-default {
    @apply card;
    padding: var(--spacing-card-default);
    border-radius: var(--radius-card);
  }

  .card-large {
    @apply card;
    padding: var(--spacing-card-large);
    border-radius: var(--radius-large);
  }

  .card-interactive {
    @apply card cursor-pointer;
    @apply hover:-translate-y-2 hover:scale-[1.02] hover:shadow-2xl;
    @apply active:scale-[0.98] transition-all duration-300;
    background: var(--gradient-card);
  }

  .card-glow {
    @apply card;
    box-shadow: var(--shadow-glow), var(--shadow-card);
  }

  /* Dramatically Enhanced Button System */
  .btn {
    @apply inline-flex items-center justify-center text-sm font-semibold;
    @apply transition-all duration-300 focus-visible:outline-none focus-visible:ring-2;
    @apply focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
    @apply active:scale-95 hover:scale-105 hover:-translate-y-0.5;
    border-radius: var(--radius-button);
    box-shadow: var(--shadow-card);
  }

  .btn:hover {
    box-shadow: var(--shadow-card-hover);
  }

  .btn-primary {
    @apply btn text-white;
    background: var(--gradient-primary);
    height: 3rem; /* 48px */
    padding: var(--spacing-md) var(--spacing-xl);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 50%, #1e3a8a 100%);
    box-shadow: var(--shadow-glow), var(--shadow-card-hover);
  }

  .btn-secondary {
    @apply btn border border-gray-200 dark:border-gray-700;
    background: var(--gradient-card);
    @apply text-gray-700 dark:text-gray-300;
    height: 3rem; /* 48px */
    padding: var(--spacing-md) var(--spacing-xl);
  }

  .btn-outline {
    @apply btn border-2 border-primary-500 bg-transparent text-primary-600;
    @apply hover:bg-primary-50 dark:hover:bg-primary-900/20;
    height: 3rem; /* 48px */
    padding: var(--spacing-md) var(--spacing-xl);
  }

  .btn-ghost {
    @apply btn bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800;
    @apply text-gray-700 dark:text-gray-300;
    height: 3rem; /* 48px */
    padding: var(--spacing-md) var(--spacing-xl);
    box-shadow: none;
  }

  .btn-sm {
    height: 2.5rem; /* 40px */
    padding: var(--spacing-sm) var(--spacing-lg);
    @apply text-sm;
  }

  .btn-lg {
    height: 3.5rem; /* 56px */
    padding: var(--spacing-lg) var(--spacing-2xl);
    @apply text-lg;
  }

  /* Gradient Backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-surface {
    background: var(--gradient-surface);
  }

  .bg-gradient-sidebar {
    background: var(--gradient-sidebar);
  }

  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  /* Enhanced Input System */
  .input {
    @apply flex h-10 w-full rounded-input border border-gray-300 bg-white px-3 py-2 text-sm;
    @apply file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2;
    @apply disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
    @apply dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
    @apply hover:border-gray-400 focus:border-primary-500;
  }

  .input-error {
    @apply input border-red-500 focus-visible:ring-red-500;
  }

  /* Enhanced Card Content Structure */
  .card-header {
    @apply flex flex-col;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  }

  .card-title {
    @apply text-xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100;
  }

  .card-description {
    @apply text-sm text-gray-600 dark:text-gray-400;
  }

  .card-content {
    padding: 0 var(--spacing-xl) var(--spacing-xl);
  }

  .card-footer {
    @apply flex items-center;
    padding: 0 var(--spacing-xl) var(--spacing-xl);
  }

  /* Enhanced Badge System */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply hover:scale-105 active:scale-95;
  }

  .badge-default {
    @apply badge border-transparent bg-gradient-to-r from-primary-600 to-primary-700 text-white;
    @apply hover:from-primary-700 hover:to-primary-800 shadow-sm hover:shadow-md;
  }

  .badge-secondary {
    @apply badge border-transparent bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-900;
    @apply hover:from-secondary-200 hover:to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 dark:text-secondary-100;
  }

  .badge-success {
    @apply badge border-transparent bg-gradient-to-r from-green-500 to-green-600 text-white;
    @apply hover:from-green-600 hover:to-green-700;
  }

  .badge-warning {
    @apply badge border-transparent bg-gradient-to-r from-yellow-500 to-yellow-600 text-white;
    @apply hover:from-yellow-600 hover:to-yellow-700;
  }

  .badge-error {
    @apply badge border-transparent bg-gradient-to-r from-red-500 to-red-600 text-white;
    @apply hover:from-red-600 hover:to-red-700;
  }

  .badge-outline {
    @apply badge text-gray-900 border-gray-300 hover:bg-gray-50;
    @apply dark:text-gray-100 dark:border-gray-600 dark:hover:bg-gray-700;
  }

  /* Enhanced Loading System */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  .spinner-lg {
    @apply spinner w-8 h-8;
  }

  .spinner-md {
    @apply spinner w-6 h-6;
  }

  .spinner-sm {
    @apply spinner w-4 h-4;
  }

  /* Enhanced Sidebar Styles */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64;
    @apply transform transition-all duration-300 ease-in-out z-50;
    background: var(--gradient-sidebar);
    box-shadow: var(--shadow-sidebar);
    backdrop-filter: blur(20px);
  }

  .sidebar-collapsed {
    @apply w-16;
  }

  .sidebar-item {
    @apply flex items-center text-slate-200 hover:bg-white/15;
    @apply transition-all duration-300 cursor-pointer;
    padding: var(--spacing-lg);
    margin: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-button);
  }

  .sidebar-item:hover {
    @apply text-white transform translate-x-1;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
  }

  .sidebar-item-active {
    @apply text-white font-semibold;
    background: rgb(var(--color-primary) / 0.2);
    border-left: 3px solid rgb(var(--color-primary));
    box-shadow: 0 4px 12px rgb(var(--color-primary) / 0.3);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Glass Effect */
  .glass {
    @apply backdrop-blur-md bg-white/80 dark:bg-gray-900/80;
    @apply border border-white/20 dark:border-gray-700/20;
  }

  /* Gradient Text */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-800 bg-clip-text text-transparent;
  }

  /* Animation Utilities */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25;
  }

  /* Enhanced Layout Utilities */
  .container-app {
    @apply max-w-7xl mx-auto;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  @media (min-width: 640px) {
    .container-app {
      padding-left: var(--spacing-xl);
      padding-right: var(--spacing-xl);
    }
  }

  @media (min-width: 1024px) {
    .container-app {
      padding-left: var(--spacing-2xl);
      padding-right: var(--spacing-2xl);
    }
  }

  .section-padding {
    padding-top: var(--spacing-2xl);
    padding-bottom: var(--spacing-2xl);
  }

  @media (min-width: 640px) {
    .section-padding {
      padding-top: var(--spacing-4xl);
      padding-bottom: var(--spacing-4xl);
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-top: var(--spacing-5xl);
      padding-bottom: var(--spacing-5xl);
    }
  }

  .page-header {
    margin-bottom: var(--spacing-2xl);
  }

  @media (min-width: 640px) {
    .page-header {
      margin-bottom: var(--spacing-3xl);
    }
  }

  .content-section {
    margin-bottom: var(--spacing-xl);
  }

  @media (min-width: 640px) {
    .content-section {
      margin-bottom: var(--spacing-2xl);
    }
  }

  /* Enhanced Grid System */
  .card-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
    gap: var(--spacing-lg);
  }

  @media (min-width: 640px) {
    .card-grid {
      gap: var(--spacing-xl);
    }
  }

  .card-grid-large {
    @apply grid grid-cols-1 lg:grid-cols-2;
    gap: var(--spacing-xl);
  }

  @media (min-width: 1024px) {
    .card-grid-large {
      gap: var(--spacing-2xl);
    }
  }

  .card-grid-compact {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
    gap: var(--spacing-lg);
  }

  .card-grid-auto {
    @apply grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
  }

  .card-grid-responsive {
    @apply grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
  }

  @media (min-width: 640px) {
    .card-grid-responsive {
      gap: var(--spacing-xl);
    }
  }

  .sidebar-mobile {
    @apply lg:translate-x-0;
  }

  /* Enhanced Typography System */
  .text-display-xl {
    @apply text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight;
  }

  .text-display-lg {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
  }

  .text-display-md {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight;
  }

  .text-heading-xl {
    @apply text-2xl sm:text-3xl font-semibold tracking-tight;
  }

  .text-heading-lg {
    @apply text-xl sm:text-2xl font-semibold tracking-tight;
  }

  .text-heading-md {
    @apply text-lg sm:text-xl font-semibold tracking-tight;
  }

  .text-heading-sm {
    @apply text-base sm:text-lg font-semibold tracking-tight;
  }

  .text-body-lg {
    @apply text-lg leading-relaxed;
  }

  .text-body-md {
    @apply text-base leading-relaxed;
  }

  .text-body-sm {
    @apply text-sm leading-relaxed;
  }

  .text-caption {
    @apply text-xs text-gray-600 dark:text-gray-400;
  }

  /* Responsive Text (Legacy - kept for compatibility) */
  .text-responsive-xl {
    @apply text-display-lg;
  }

  .text-responsive-lg {
    @apply text-heading-xl;
  }

  .text-responsive-md {
    @apply text-heading-lg;
  }

  /* Responsive Spacing */
  .space-responsive {
    gap: var(--spacing-lg);
  }

  @media (min-width: 640px) {
    .space-responsive {
      gap: var(--spacing-xl);
    }
  }

  @media (min-width: 1024px) {
    .space-responsive {
      gap: var(--spacing-2xl);
    }
  }

  .gap-responsive {
    gap: var(--spacing-lg);
  }

  @media (min-width: 640px) {
    .gap-responsive {
      gap: var(--spacing-xl);
    }
  }

  @media (min-width: 1024px) {
    .gap-responsive {
      gap: var(--spacing-2xl);
    }
  }

  /* Consistent spacing utilities */
  .spacing-xs { gap: var(--spacing-xs); }
  .spacing-sm { gap: var(--spacing-sm); }
  .spacing-md { gap: var(--spacing-md); }
  .spacing-lg { gap: var(--spacing-lg); }
  .spacing-xl { gap: var(--spacing-xl); }
  .spacing-2xl { gap: var(--spacing-2xl); }
  .spacing-3xl { gap: var(--spacing-3xl); }
  .spacing-4xl { gap: var(--spacing-4xl); }
  .spacing-5xl { gap: var(--spacing-5xl); }

  /* Consistent icon sizing */
  .icon-xs { @apply w-3 h-3; }
  .icon-sm { @apply w-4 h-4; }
  .icon-md { @apply w-5 h-5; }
  .icon-lg { @apply w-6 h-6; }
  .icon-xl { @apply w-8 h-8; }
  .icon-2xl { @apply w-10 h-10; }
  .icon-3xl { @apply w-12 h-12; }
}

/* Additional Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
