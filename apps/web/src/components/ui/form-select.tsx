'use client';

import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import { forwardRef } from 'react';

interface FormSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  hint?: string;
  variant?: 'default' | 'floating' | 'minimal';
  icon?: React.ReactNode;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
}

export const FormSelect = forwardRef<HTMLSelectElement, FormSelectProps>(
  ({ 
    className, 
    label, 
    error, 
    hint, 
    variant = 'default',
    icon,
    options,
    disabled,
    ...props 
  }, ref) => {
    if (variant === 'floating') {
      return (
        <div className="relative">
          <div className="relative">
            {icon && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-10">
                {icon}
              </div>
            )}
            <select
              className={cn(
                'input peer w-full px-4 py-4 appearance-none cursor-pointer',
                error && 'input-error',
                icon && 'pl-12',
                'pr-12',
                className
              )}
              ref={ref}
              disabled={disabled}
              {...props}
            >
              {options.map((option) => (
                <option 
                  key={option.value} 
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </option>
              ))}
            </select>
            <label
              className={cn(
                'absolute left-4 top-2 text-xs transition-all duration-200 pointer-events-none',
                'text-primary-600',
                icon && 'left-12',
                error && 'text-red-600'
              )}
            >
              {label}
            </label>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <ChevronDown className="w-5 h-5 text-gray-400" />
            </div>
          </div>
          {(error || hint) && (
            <div className="mt-2">
              {error && (
                <p className="text-sm text-red-600 flex items-center">
                  <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                  {error}
                </p>
              )}
              {hint && !error && (
                <p className="text-sm text-gray-500">{hint}</p>
              )}
            </div>
          )}
        </div>
      );
    }

    if (variant === 'minimal') {
      return (
        <div className="relative">
          <div className="relative">
            {icon && (
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 text-gray-400">
                {icon}
              </div>
            )}
            <select
              className={cn(
                'w-full py-3 text-gray-900 bg-transparent border-0 border-b-2 border-gray-200',
                'focus:ring-0 focus:border-primary-500 transition-all duration-200',
                'appearance-none cursor-pointer hover:border-gray-400',
                'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
                error && 'border-red-500 focus:border-red-500',
                icon && 'pl-8',
                'pr-8',
                className
              )}
              ref={ref}
              disabled={disabled}
              {...props}
            >
              {options.map((option) => (
                <option 
                  key={option.value} 
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </option>
              ))}
            </select>
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <ChevronDown className="w-5 h-5 text-gray-400" />
            </div>
          </div>
          {(error || hint) && (
            <div className="mt-2">
              {error && (
                <p className="text-sm text-red-600">{error}</p>
              )}
              {hint && !error && (
                <p className="text-sm text-gray-500">{hint}</p>
              )}
            </div>
          )}
        </div>
      );
    }

    // Default variant
    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-semibold text-gray-800">
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {icon}
            </div>
          )}
          <select
            className={cn(
              'input w-full px-4 py-3 appearance-none cursor-pointer',
              error && 'input-error',
              icon && 'pl-12',
              'pr-12',
              className
            )}
            ref={ref}
            disabled={disabled}
            {...props}
          >
            {options.map((option) => (
              <option 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <ChevronDown className="w-5 h-5 text-gray-400" />
          </div>
        </div>
        {(error || hint) && (
          <div>
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                {error}
              </p>
            )}
            {hint && !error && (
              <p className="text-sm text-gray-500">{hint}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

FormSelect.displayName = 'FormSelect';
