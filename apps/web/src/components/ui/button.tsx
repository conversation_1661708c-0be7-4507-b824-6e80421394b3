'use client';

import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import { ButtonHTMLAttributes, forwardRef, ReactNode } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  children: ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    loading = false,
    leftIcon,
    rightIcon,
    disabled,
    children,
    ...props 
  }, ref) => {
    const baseClasses = 'btn';
    
    const variantClasses = {
      primary: 'btn-primary',
      secondary: 'btn-secondary',
      outline: 'btn-outline',
      ghost: 'btn-ghost',
      destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-md hover:shadow-lg',
    };

    const sizeClasses = {
      sm: 'btn-sm',
      md: '', // Default size is handled by btn-primary, btn-secondary, etc.
      lg: 'btn-lg',
      xl: 'btn-lg text-lg', // Use large button with larger text
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          isDisabled && 'opacity-50 cursor-not-allowed',
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        <div className="flex items-center justify-center space-x-2">
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : leftIcon ? (
            <span className="flex-shrink-0">{leftIcon}</span>
          ) : null}
          
          <span>{children}</span>
          
          {!loading && rightIcon && (
            <span className="flex-shrink-0">{rightIcon}</span>
          )}
        </div>
      </button>
    );
  }
);
Button.displayName = 'Button';

// Icon Button Component
interface IconButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon: ReactNode;
  'aria-label': string;
}

const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ 
    className, 
    variant = 'ghost', 
    size = 'md', 
    loading = false,
    icon,
    disabled,
    ...props 
  }, ref) => {
    const baseClasses = 'btn';
    
    const variantClasses = {
      primary: 'btn-primary',
      secondary: 'btn-secondary',
      outline: 'btn-outline',
      ghost: 'btn-ghost',
      destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-md hover:shadow-lg',
    };

    const sizeClasses = {
      sm: 'w-8 h-8 p-0',
      md: 'w-10 h-10 p-0',
      lg: 'w-12 h-12 p-0',
    };

    const iconSizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          'flex items-center justify-center',
          isDisabled && 'opacity-50 cursor-not-allowed',
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {loading ? (
          <Loader2 className={cn('animate-spin', iconSizeClasses[size])} />
        ) : (
          <span className={iconSizeClasses[size]}>{icon}</span>
        )}
      </button>
    );
  }
);
IconButton.displayName = 'IconButton';

// Button Group Component
interface ButtonGroupProps {
  children: ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

const ButtonGroup = ({ children, className, orientation = 'horizontal' }: ButtonGroupProps) => {
  return (
    <div 
      className={cn(
        'inline-flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        '[&>button]:rounded-none [&>button:first-child]:rounded-l-button [&>button:last-child]:rounded-r-button',
        orientation === 'vertical' && '[&>button:first-child]:rounded-t-button [&>button:first-child]:rounded-l-none [&>button:last-child]:rounded-b-button [&>button:last-child]:rounded-r-none',
        '[&>button:not(:first-child)]:border-l-0',
        orientation === 'vertical' && '[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0',
        className
      )}
    >
      {children}
    </div>
  );
};

// Toggle Button Component
interface ToggleButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  pressed?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: ReactNode;
}

const ToggleButton = forwardRef<HTMLButtonElement, ToggleButtonProps>(
  ({ 
    className, 
    variant = 'outline', 
    size = 'md', 
    pressed = false,
    children,
    ...props 
  }, ref) => {
    const baseClasses = 'btn';
    
    const variantClasses = {
      primary: pressed ? 'btn-primary' : 'btn-outline',
      secondary: pressed ? 'btn-secondary' : 'btn-outline',
      outline: pressed ? 'bg-primary-100 border-primary-500 text-primary-700' : 'btn-outline',
    };

    const sizeClasses = {
      sm: 'btn-sm',
      md: '', // Default size handled by variant classes
      lg: 'btn-lg',
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        aria-pressed={pressed}
        {...props}
      >
        {children}
      </button>
    );
  }
);
ToggleButton.displayName = 'ToggleButton';

export { Button, ButtonGroup, IconButton, ToggleButton };

