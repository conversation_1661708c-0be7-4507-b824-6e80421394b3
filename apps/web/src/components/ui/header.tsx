'use client';

import { useAuth } from '@/lib/auth';
import { PublicHeader } from './public-header';

export function Header() {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state or nothing while checking auth
  if (isLoading) {
    return (
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-colors">
        <div className="container-app">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-card flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="text-2xl font-bold text-gradient-primary">Revalu</span>
            </div>
          </div>
        </div>
      </header>
    );
  }

  // Render appropriate header based on authentication status
  return isAuthenticated ? <AuthenticatedHeader /> : <PublicHeader />;
}


