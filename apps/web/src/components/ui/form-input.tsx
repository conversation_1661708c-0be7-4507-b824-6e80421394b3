'use client';

import { cn } from '@/lib/utils';
import { Eye, EyeOff } from 'lucide-react';
import { forwardRef, useState } from 'react';

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  hint?: string;
  variant?: 'default' | 'floating' | 'minimal';
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  ({ 
    className, 
    type = 'text', 
    label, 
    error, 
    hint, 
    variant = 'default',
    icon,
    rightIcon,
    disabled,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);

    const isPassword = type === 'password';
    const inputType = isPassword && showPassword ? 'text' : type;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(!!e.target.value);
      props.onChange?.(e);
    };

    if (variant === 'floating') {
      return (
        <div className="relative">
          <div className="relative">
            {icon && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-10">
                {icon}
              </div>
            )}
            <input
              type={inputType}
              className={cn(
                'input peer w-full px-4 py-4 text-gray-900',
                'placeholder-transparent transition-all duration-200',
                error && 'input-error',
                icon && 'pl-12',
                (isPassword || rightIcon) && 'pr-12',
                className
              )}
              placeholder={label}
              ref={ref}
              disabled={disabled}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onChange={handleChange}
              {...props}
            />
            <label
              className={cn(
                'absolute left-4 transition-all duration-200 pointer-events-none',
                'peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:text-gray-400',
                'peer-focus:top-2 peer-focus:text-xs peer-focus:text-primary-600',
                'top-2 text-xs',
                (isFocused || hasValue) ? 'text-primary-600' : 'text-gray-400',
                icon && 'peer-placeholder-shown:left-12 peer-focus:left-4',
                error && 'peer-focus:text-red-600'
              )}
            >
              {label}
            </label>
            {(isPassword || rightIcon) && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {isPassword ? (
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    disabled={disabled}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                ) : (
                  rightIcon
                )}
              </div>
            )}
          </div>
          {(error || hint) && (
            <div className="mt-2">
              {error && (
                <p className="text-sm text-red-600 flex items-center">
                  <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                  {error}
                </p>
              )}
              {hint && !error && (
                <p className="text-sm text-gray-500">{hint}</p>
              )}
            </div>
          )}
        </div>
      );
    }

    if (variant === 'minimal') {
      return (
        <div className="relative">
          <div className="relative">
            {icon && (
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 text-gray-400">
                {icon}
              </div>
            )}
            <input
              type={inputType}
              className={cn(
                'w-full py-3 text-gray-900 bg-transparent border-0 border-b-2 border-gray-200',
                'focus:ring-0 focus:border-primary-500 transition-all duration-200',
                'placeholder-gray-400 hover:border-gray-400',
                'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
                error && 'border-red-500 focus:border-red-500',
                icon && 'pl-8',
                (isPassword || rightIcon) && 'pr-8',
                className
              )}
              placeholder={label}
              ref={ref}
              disabled={disabled}
              onChange={handleChange}
              {...props}
            />
            {(isPassword || rightIcon) && (
              <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                {isPassword ? (
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    disabled={disabled}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                ) : (
                  rightIcon
                )}
              </div>
            )}
          </div>
          {(error || hint) && (
            <div className="mt-2">
              {error && (
                <p className="text-sm text-red-600">{error}</p>
              )}
              {hint && !error && (
                <p className="text-sm text-gray-500">{hint}</p>
              )}
            </div>
          )}
        </div>
      );
    }

    // Default variant
    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-semibold text-gray-800">
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {icon}
            </div>
          )}
          <input
            type={inputType}
            className={cn(
              'input w-full px-4 py-3',
              error && 'input-error',
              icon && 'pl-12',
              (isPassword || rightIcon) && 'pr-12',
              className
            )}
            placeholder={props.placeholder}
            ref={ref}
            disabled={disabled}
            onChange={handleChange}
            {...props}
          />
          {(isPassword || rightIcon) && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {isPassword ? (
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  disabled={disabled}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              ) : (
                rightIcon
              )}
            </div>
          )}
        </div>
        {(error || hint) && (
          <div>
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                {error}
              </p>
            )}
            {hint && !error && (
              <p className="text-sm text-gray-500">{hint}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

FormInput.displayName = 'FormInput';
