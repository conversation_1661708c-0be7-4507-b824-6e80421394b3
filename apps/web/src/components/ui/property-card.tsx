'use client';

import { formatCurrency } from '@/lib/utils';
import {
    BarChart3,
    Bath,
    Bed,
    Building,
    Car,
    Heart,
    MapPin,
    TrendingUp
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

interface PropertyCardProps {
  property: {
    id: string;
    address: string;
    suburb: string;
    state: string;
    price: number;
    bedrooms?: number;
    bathrooms?: number;
    parking?: number;
    propertyType: string;
    imageUrl?: string;
    investmentScore?: number;
    developmentScore?: number;
    growthPotential?: number;
    isTracked?: boolean;
  };
  variant?: 'default' | 'compact' | 'featured';
  showActions?: boolean;
  onTrack?: (propertyId: string) => void;
  onCompare?: (propertyId: string) => void;
}

export function PropertyCard({ 
  property, 
  variant = 'default', 
  showActions = true,
  onTrack,
  onCompare 
}: PropertyCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isTracked, setIsTracked] = useState(property.isTracked || false);

  const handleTrack = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsTracked(!isTracked);
    onTrack?.(property.id);
  };

  const handleCompare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onCompare?.(property.id);
  };

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-400';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score?: number) => {
    if (!score) return 'bg-gray-200';
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (variant === 'compact') {
    return (
      <Link href={`/properties/${property.id}`}>
        <div className="card-interactive overflow-hidden group">
          <div className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold text-gray-900 text-sm group-hover:text-primary-600 transition-colors">
                {property.address}
              </h3>
              <span className="text-lg font-bold text-primary-600">
                {formatCurrency(property.price)}
              </span>
            </div>
            <p className="text-gray-600 text-sm mb-3">
              {property.suburb}, {property.state}
            </p>

            {property.investmentScore && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Investment Score</span>
                <span className={`text-sm font-bold ${getScoreColor(property.investmentScore)}`}>
                  {property.investmentScore}/100
                </span>
              </div>
            )}
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link href={`/properties/${property.id}`}>
      <div
        className="card-interactive overflow-hidden group hover:-translate-y-2"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image Section */}
        <div className="relative h-48 bg-gradient-to-br from-blue-400 to-blue-600 overflow-hidden">
          {property.imageUrl ? (
            <Image
              src={property.imageUrl}
              alt={property.address}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-white text-center">
                <Building className="w-12 h-12 mx-auto mb-2 opacity-80" />
                <p className="text-sm opacity-80">Property Image</p>
              </div>
            </div>
          )}
          
          {/* Overlay Actions */}
          {showActions && (
            <div className={`absolute top-4 right-4 flex space-x-2 transition-opacity duration-200 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`}>
              <button
                onClick={handleTrack}
                className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
                  isTracked 
                    ? 'bg-red-500 text-white' 
                    : 'bg-white/90 text-gray-700 hover:bg-white'
                }`}
              >
                <Heart className={`w-4 h-4 ${isTracked ? 'fill-current' : ''}`} />
              </button>
              <button
                onClick={handleCompare}
                className="w-8 h-8 bg-white/90 hover:bg-white text-gray-700 rounded-full flex items-center justify-center transition-all duration-200"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>
          )}

          {/* Property Type Badge */}
          <div className="absolute top-4 left-4">
            <span className="bg-white/90 text-gray-800 text-xs font-medium px-3 py-1 rounded-full">
              {property.propertyType}
            </span>
          </div>
        </div>

        {/* Content Section */}
        <div className="card-content">
          <div className="flex justify-between items-start mb-3">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
              {property.address}
            </h3>
            <span className="text-xl font-bold text-primary-600">
              {formatCurrency(property.price)}
            </span>
          </div>
          
          <div className="flex items-center text-gray-600 mb-4">
            <MapPin className="w-4 h-4 mr-1" />
            <span className="text-sm">{property.suburb}, {property.state}</span>
          </div>

          {/* Property Details */}
          {(property.bedrooms || property.bathrooms || property.parking) && (
            <div className="flex items-center space-x-4 mb-4 text-sm text-gray-600">
              {property.bedrooms && (
                <div className="flex items-center">
                  <Bed className="w-4 h-4 mr-1" />
                  <span>{property.bedrooms}</span>
                </div>
              )}
              {property.bathrooms && (
                <div className="flex items-center">
                  <Bath className="w-4 h-4 mr-1" />
                  <span>{property.bathrooms}</span>
                </div>
              )}
              {property.parking && (
                <div className="flex items-center">
                  <Car className="w-4 h-4 mr-1" />
                  <span>{property.parking}</span>
                </div>
              )}
            </div>
          )}

          {/* Scores */}
          <div className="space-y-3">
            {property.investmentScore && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Investment Score</span>
                <div className="flex items-center">
                  <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      className={`h-2 rounded-full ${getScoreBgColor(property.investmentScore)}`}
                      style={{ width: `${property.investmentScore}%` }}
                    ></div>
                  </div>
                  <span className={`text-sm font-medium ${getScoreColor(property.investmentScore)}`}>
                    {property.investmentScore}/100
                  </span>
                </div>
              </div>
            )}
            
            {property.growthPotential && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Growth Potential</span>
                <div className="flex items-center">
                  <TrendingUp className="w-4 h-4 mr-1 text-green-600" />
                  <span className="text-sm font-medium text-green-600">
                    +{property.growthPotential}%
                  </span>
                </div>
              </div>
            )}
            
            {property.developmentScore && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Development Score</span>
                <span className={`text-sm font-medium ${getScoreColor(property.developmentScore)}`}>
                  {property.developmentScore}/100
                </span>
              </div>
            )}
          </div>

          {/* Action Button */}
          <button className="w-full mt-4 btn-primary py-3 rounded-xl shadow-lg hover:shadow-xl group-hover:scale-105">
            View Full Analysis
          </button>
        </div>
      </div>
    </Link>
  );
}
